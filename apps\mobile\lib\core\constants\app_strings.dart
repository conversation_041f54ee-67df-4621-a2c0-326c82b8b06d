class AppStrings {
  // General
  static const appName = 'Dayliz';
  
  // Login & Authentication
  static const login = 'Login';
  static const register = 'Register';
  static const email = 'Email';
  static const password = 'Password';
  static const confirmPassword = 'Confirm Password';
  static const forgotPassword = 'Forgot Password?';
  static const dontHaveAccount = "Don't have an account?";
  static const alreadyHaveAccount = 'Already have an account?';
  
  // Products
  static const products = 'Products';
  static const relatedProducts = 'Related Products';
  static const productDetails = 'Product Details';
  static const addToCart = 'Add to Cart';
  static const browseProducts = 'Browse Products';
  static const price = 'Price';
  static const rating = 'Rating';
  static const description = 'Description';
  static const outOfStock = 'Out of Stock';
  static const inStock = 'In Stock';
  
  // Cart
  static const cart = 'Cart';
  static const emptyCartTitle = 'Your Cart is Empty';
  static const emptyCartMessage = 'Looks like you haven\'t added anything to your cart yet. Start shopping to add items.';
  static const removeFromCart = 'Remove from Cart';
  static const checkout = 'Checkout';
  static const totalItems = 'Total Items';
  static const subtotal = 'Subtotal';
  static const total = 'Total';
  static const clearCart = 'Clear Cart';
  static const clearCartTitle = 'Clear Cart';
  static const clearCartConfirmation = 'Are you sure you want to remove all items from your cart?';
  static const cancel = 'Cancel';
  static const clear = 'Clear';
  static const continueShopping = 'Continue Shopping';
  static const proceedToCheckout = 'Proceed to Checkout';
  static const errorRemovingFromCart = 'Error removing item from cart';
  static const errorUpdatingCart = 'Error updating cart';
  static const errorClearingCart = 'Error clearing cart';
  
  // Error Messages
  static const errorOccurred = 'An error occurred';
  static const serverError = 'Server error occurred';
  static const networkError = 'Network error occurred';
  static const authError = 'Authentication error';
  static const noResultsFound = 'No results found';
  static const tryAgain = 'Try Again';
  
  // Form Validation
  static const requiredField = 'This field is required';
  static const invalidEmail = 'Please enter a valid email';
  static const invalidPassword = 'Password must be at least 6 characters';
  static const passwordsDoNotMatch = 'Passwords do not match';
  static const invalidPhoneNumber = 'Please enter a valid phone number';
  static const invalidName = 'Name must be at least 2 characters';
} 