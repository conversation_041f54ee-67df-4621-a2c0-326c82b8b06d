This is a test file to verify Context7 MCP server integration.

// Test code completion with Context7
// Try typing a partial Flutter widget below and see if Context7 provides intelligent completions:

// Example: Sta

// Test code explanation with Context7
// Ask Context7 to explain this Flutter code:

class MyWidget extends StatelessWidget {
  const MyWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Text('Hello, Context7!'),
    );
  }
}
