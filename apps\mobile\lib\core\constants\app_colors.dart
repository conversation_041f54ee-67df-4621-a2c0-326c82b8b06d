import 'package:flutter/material.dart';

class AppColors {
  // Primary colors
  static const primary = Color(0xFF0066CC);
  static const primaryLight = Color(0xFF4D94FF);
  static const primaryDark = Color(0xFF003D7A);
  
  // Secondary colors
  static const secondary = Color(0xFFFF9500);
  static const secondaryLight = Color(0xFFFFB84D);
  static const secondaryDark = Color(0xFFCC7600);
  
  // Neutral colors
  static const background = Color(0xFFF8F9FA);
  static const surface = Colors.white;
  static const grey = Color(0xFF9E9E9E);
  static const greyLight = Color(0xFFE0E0E0);
  static const greyDark = Color(0xFF616161);
  
  // Text colors
  static const textPrimary = Color(0xFF212121);
  static const textSecondary = Color(0xFF757575);
  static const textDisabled = Color(0xFFBDBDBD);
  
  // Semantic colors
  static const error = Color(0xFFD32F2F);
  static const success = Color(0xFF388E3C);
  static const info = Color(0xFF1976D2);
  static const warning = Color(0xFFFFA000);
  
  // Other colors
  static const divider = Color(0xFFEEEEEE);
  static const cardShadow = Color(0x1A000000);
  static const ratingStarFilled = Color(0xFFFFA000);
  static const ratingStarEmpty = Color(0xFFE0E0E0);
  static const discount = Color(0xFFD32F2F);
} 