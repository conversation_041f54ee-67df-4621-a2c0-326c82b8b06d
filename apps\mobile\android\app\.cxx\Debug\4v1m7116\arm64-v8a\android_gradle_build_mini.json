{"buildFiles": ["C:\\Users\\<USER>\\Cursor\\Flutter\\flutter_windows_3.29.2-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Project_dayliz\\Dayliz_App\\android\\app\\.cxx\\Debug\\4v1m7116\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Project_dayliz\\Dayliz_App\\android\\app\\.cxx\\Debug\\4v1m7116\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}