// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in dayliz_app/test/presentation/providers/auth_providers_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:dartz/dartz.dart' as _i3;
import 'package:dayliz_app/core/errors/failures.dart' as _i6;
import 'package:dayliz_app/domain/entities/user.dart' as _i7;
import 'package:dayliz_app/domain/repositories/auth_repository.dart' as _i2;
import 'package:dayliz_app/domain/usecases/change_password_usecase.dart'
    as _i14;
import 'package:dayliz_app/domain/usecases/forgot_password_usecase.dart'
    as _i12;
import 'package:dayliz_app/domain/usecases/get_current_user_usecase.dart'
    as _i10;
import 'package:dayliz_app/domain/usecases/is_authenticated_usecase.dart'
    as _i11;
import 'package:dayliz_app/domain/usecases/login_usecase.dart' as _i8;
import 'package:dayliz_app/domain/usecases/logout_usecase.dart' as _i9;
import 'package:dayliz_app/domain/usecases/register_usecase.dart' as _i4;
import 'package:dayliz_app/domain/usecases/reset_password_usecase.dart' as _i13;
import 'package:dayliz_app/domain/usecases/sign_in_with_google_usecase.dart'
    as _i15;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthRepository_0 extends _i1.SmartFake
    implements _i2.AuthRepository {
  _FakeAuthRepository_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [RegisterUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockRegisterUseCase extends _i1.Mock implements _i4.RegisterUseCase {
  MockRegisterUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i7.User>> call(
          _i4.RegisterParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i5.Future<_i3.Either<_i6.Failure, _i7.User>>.value(
            _FakeEither_1<_i6.Failure, _i7.User>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, _i7.User>>);
}

/// A class which mocks [LoginUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginUseCase extends _i1.Mock implements _i8.LoginUseCase {
  MockLoginUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i7.User>> call(_i8.LoginParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i5.Future<_i3.Either<_i6.Failure, _i7.User>>.value(
            _FakeEither_1<_i6.Failure, _i7.User>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, _i7.User>>);
}

/// A class which mocks [LogoutUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogoutUseCase extends _i1.Mock implements _i9.LogoutUseCase {
  MockLogoutUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, bool>> call() => (super.noSuchMethod(
        Invocation.method(
          #call,
          [],
        ),
        returnValue: _i5.Future<_i3.Either<_i6.Failure, bool>>.value(
            _FakeEither_1<_i6.Failure, bool>(
          this,
          Invocation.method(
            #call,
            [],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, bool>>);
}

/// A class which mocks [GetCurrentUserUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetCurrentUserUseCase extends _i1.Mock
    implements _i10.GetCurrentUserUseCase {
  MockGetCurrentUserUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i7.User>> call() => (super.noSuchMethod(
        Invocation.method(
          #call,
          [],
        ),
        returnValue: _i5.Future<_i3.Either<_i6.Failure, _i7.User>>.value(
            _FakeEither_1<_i6.Failure, _i7.User>(
          this,
          Invocation.method(
            #call,
            [],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, _i7.User>>);
}

/// A class which mocks [IsAuthenticatedUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockIsAuthenticatedUseCase extends _i1.Mock
    implements _i11.IsAuthenticatedUseCase {
  MockIsAuthenticatedUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<bool> call() => (super.noSuchMethod(
        Invocation.method(
          #call,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);
}

/// A class which mocks [ForgotPasswordUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockForgotPasswordUseCase extends _i1.Mock
    implements _i12.ForgotPasswordUseCase {
  MockForgotPasswordUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, bool>> call(
          _i12.ForgotPasswordParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i5.Future<_i3.Either<_i6.Failure, bool>>.value(
            _FakeEither_1<_i6.Failure, bool>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, bool>>);
}

/// A class which mocks [ResetPasswordUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockResetPasswordUseCase extends _i1.Mock
    implements _i13.ResetPasswordUseCase {
  MockResetPasswordUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, bool>> call(
          _i13.ResetPasswordParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i5.Future<_i3.Either<_i6.Failure, bool>>.value(
            _FakeEither_1<_i6.Failure, bool>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, bool>>);
}

/// A class which mocks [ChangePasswordUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockChangePasswordUseCase extends _i1.Mock
    implements _i14.ChangePasswordUseCase {
  MockChangePasswordUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, bool>> call(
          _i14.ChangePasswordParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i5.Future<_i3.Either<_i6.Failure, bool>>.value(
            _FakeEither_1<_i6.Failure, bool>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, bool>>);
}

/// A class which mocks [SignInWithGoogleUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockSignInWithGoogleUseCase extends _i1.Mock
    implements _i15.SignInWithGoogleUseCase {
  MockSignInWithGoogleUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i7.User>> call(_i15.NoParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i5.Future<_i3.Either<_i6.Failure, _i7.User>>.value(
            _FakeEither_1<_i6.Failure, _i7.User>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i3.Either<_i6.Failure, _i7.User>>);
}
