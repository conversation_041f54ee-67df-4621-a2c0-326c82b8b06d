# Assets Directory

This directory contains placeholder files to prevent asset loading errors.

## Missing Assets

The following assets need to be added:

### Images
- `app_logo.png` - Main app logo
- `splash_logo.png` - Splash screen logo  
- `empty_cart.png` - Empty cart illustration
- `empty_wishlist.png` - Empty wishlist illustration
- `empty_orders.png` - Empty orders illustration
- `empty_search.png` - Empty search illustration
- `placeholder_product.png` - Product placeholder image
- `placeholder_profile.png` - Profile placeholder image

### Icons
- `fruits.png` - Fruits category icon
- `vegetables.png` - Vegetables category icon
- `dairy.png` - Dairy category icon
- `bakery.png` - Bakery category icon
- `meat.png` - Meat category icon
- `cash.png` - Cash payment icon

## Temporary Solution

For now, you can:
1. Use placeholder images
2. Replace with actual assets when available
3. Use Icons.* from Flutter's material icons instead of PNG files
