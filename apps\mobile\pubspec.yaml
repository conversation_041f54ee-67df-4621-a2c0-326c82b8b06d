name: dayliz_app
description: A q-commerce app for daily needs.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  # Architecture
  dartz: ^0.10.1              # Functional programming
  fpdart: ^1.1.0              # Additional functional programming utilities
  equatable: ^2.0.5           # Value equality
  get_it: ^7.6.0              # Dependency injection
  flutter_riverpod: ^2.4.0    # State management
  riverpod_annotation: ^2.1.5  # Riverpod code generation
  fast_immutable_collections: ^10.2.4 # High-performance immutable collections
  flutter_hooks: ^0.20.5      # Better state management and lifecycle handling

  # Network
  http: ^1.1.0                # HTTP client
  dio: ^5.3.2                 # Alternative HTTP client with more features
  internet_connection_checker: ^1.0.0+1 # Network connectivity
  cached_network_image: ^3.2.3 # Image caching
  flutter_cache_manager: ^3.3.1 # Advanced caching for API responses and files

  # Local storage
  shared_preferences: ^2.2.1  # Simple key-value storage
  hive: ^2.2.3                # NoSQL database
  hive_flutter: ^1.1.0        # Hive Flutter integration for high-performance local storage
  sqflite: ^2.3.0             # SQLite database

  # Supabase (restored)
  supabase_flutter: ^2.0.1
  decimal: ^2.3.3

  # UI
  flutter_svg: ^2.0.7         # SVG rendering
  shimmer: ^3.0.0             # Loading effects
  lottie: ^2.6.0              # Lottie animations
  google_fonts: ^5.1.0        # Google fonts
  flutter_animate: ^4.2.0     # Animation library
  smooth_page_indicator: ^1.1.0 # Page indicator
  carousel_slider: ^4.2.1     # Carousel slider - fixed version to avoid conflict
  expandable: ^5.0.1          # Expandable widgets
  pull_to_refresh: ^2.0.0     # Pull to refresh
  infinite_scroll_pagination: ^4.0.0 # Pagination
  skeletons: ^0.0.3           # Skeleton loading
  visibility_detector: ^0.4.0+2
  timeline_tile: ^2.0.0       # Timeline visualization for order tracking
  confetti: ^0.7.0            # Confetti animation for celebrations
  flutter_staggered_grid_view: ^0.7.0 # High-performance grid layouts

  # Navigation
  go_router: ^10.1.2          # Router

  # Tools
  intl: ^0.18.1               # Internationalization
  logger: ^2.0.1              # Logging
  flutter_dotenv: ^5.1.0      # Environment variables
  uuid: ^4.5.1                # UUID generation (updated for geolocator compatibility)
  connectivity_plus: ^4.0.2   # Network connectivity
  package_info_plus: ^4.2.0   # Package information

  # UI/UX Enhancement Packages
  flutter_screenutil: ^5.9.3  # Responsive design for all screen sizes
  auto_size_text: ^3.0.0      # Responsive typography that adapts to screen
  rive: ^0.13.1               # Interactive vector animations (premium feel)
  flutter_keyboard_visibility: ^6.0.0 # Better keyboard handling

  # Location dependencies - Updated to stable versions compatible with Flutter 3.29.2
  geolocator: ^12.0.0           # Stable geolocation (compatible with Flutter 3.29.2)
  geocoding: ^3.0.0             # Latest geocoding (compatible with Flutter 3.29.2)
  # Enhanced mock implementation available as fallback

  flutter_secure_storage: ^8.0.0 # Secure storage
  url_launcher: ^6.1.14       # URL launcher
  share_plus: ^7.1.0          # Sharing content
  flutter_slidable: ^4.0.0
  image_picker: ^1.1.2
  google_sign_in: ^6.1.5      # Google Sign-In

  permission_handler: ^11.3.0 # Permission handling (updated)

  # Maps and Location - Google Maps implementation
  google_maps_flutter: ^2.9.0   # Google Maps Flutter SDK
  # mapbox_maps_flutter: ^2.3.0  # Removed - migrated back to Google Maps

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.3       # Linting
  json_serializable: ^6.7.1
  build_runner: ^2.4.15        # Code generation
  hive_generator: ^2.0.1      # Hive code generation
  riverpod_generator: ^2.3.1  # Riverpod code generation
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.9
  mocktail: ^1.0.2
  mockito: ^5.4.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    # Temporarily comment out fonts directory until we have the actual font files
    # - assets/fonts/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # Temporarily comment out font configuration
  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Poppins-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Poppins-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.png"
  min_sdk_android: 21

flutter_native_splash:
  color: "#FFFFFF"
  image: assets/images/splash_logo.png
  android_12:
    image: assets/images/splash_logo.png
    color: "#FFFFFF"
