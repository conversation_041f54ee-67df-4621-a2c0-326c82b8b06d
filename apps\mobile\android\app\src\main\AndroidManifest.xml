<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application
        android:label="Dayliz"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:enableOnBackInvokedCallback="true">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

            <!-- Deep Link Configuration -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- HTTPS scheme for App Links (more secure) -->
                <data
                    android:scheme="https"
                    android:host="dayliz.app" />
            </intent-filter>

            <!-- Custom URI scheme for general app links -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="dayliz"
                    android:host="app" />
            </intent-filter>

            <!-- Password Reset Deep Link -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="dayliz"
                    android:host="verify-email" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <!-- Google Maps API Key - Active configuration -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyBf-rFSTkhfN_Z6DB8PR4suHjimIMxxXg0" />
        <!-- Mapbox configuration - Legacy (commented out) -->
        <!-- <meta-data
            android:name="com.mapbox.token"
            android:value="${MAPBOX_ACCESS_TOKEN}" /> -->

        <!-- Enable high refresh rate support for 90/120 FPS -->
        <meta-data
            android:name="io.flutter.embedding.android.EnableHighRefreshRate"
            android:value="true" />

        <!-- Google Sign-In Activity -->
        <activity
            android:name="io.flutter.plugins.googlesignin.GoogleSignInActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:exported="true">
        </activity>

        <!-- Deep Link Handler for OAuth Redirects -->
        <activity
            android:name="com.linusu.flutter_web_auth_2.CallbackActivity"
            android:exported="true">
            <!-- Custom scheme for app redirects -->
            <intent-filter android:label="flutter_web_auth_2">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="com.dayliz.dayliz_app"
                    android:host="login" />
            </intent-filter>

            <!-- Supabase callback URL -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="https"
                    android:host="zdezerezpbeuebnompyj.supabase.co"
                    android:pathPrefix="/auth/v1/callback" />
            </intent-filter>
        </activity>
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>

    <!-- Required permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Location permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <!-- For Android 10+ foreground service -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
</manifest>
